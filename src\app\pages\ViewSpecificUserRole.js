// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { Modal, Button } from 'react-bootstrap';
import cogoToast from 'cogo-toast';

import API from '../services/API';
import {
    USERS_URL,
    USERS_URL_WITH_ID,
    EHS_ROLE_URL,
    EPTW_ROLE_URL,
    INCIDENT_ROLE_URL,
    INSPECTION_ROLE_URL,
    PLANT_ROLE_URL,
    GROUP_EHS_ROLE_URL,
    REPORT_ROLE_URL
} from '../constants';
import CardOverlay from './CardOverlay';

const ViewSpecificUserRole = () => {
    const [allUsers, setAllUsers] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedUser, setSelectedUser] = useState(null);
    const [userRoles, setUserRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] });
    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] });

    const [showModal, setShowModal] = useState(false);
    const [loading, setLoading] = useState(false);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(5);

    // Organization filter states
    const [selectedOrganization, setSelectedOrganization] = useState('');
    const [organizationDropdownOpen, setOrganizationDropdownOpen] = useState(false);
    const [organizationSearchQuery, setOrganizationSearchQuery] = useState('');



    useEffect(() => {
        getAllUsers();
        getAllRoles();
    }, []);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (organizationDropdownOpen && !event.target.closest('.dropdown')) {
                setOrganizationDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [organizationDropdownOpen]);

    const getAllUsers = async () => {
        try {
            const response = await API.get(USERS_URL);
            if (response.status === 200) {
                setAllUsers(response.data.filter(i => i.status !== false).sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())));
            }
        } catch (error) {
            cogoToast.error('Failed to fetch users');
        }
    };

    const getAllRoles = async () => {
        try {
            const [ehsRes, eptwRes, incidentRes, inspectionRes, plantRes, groupEhsRes, reportRes] = await Promise.all([
                API.get(EHS_ROLE_URL),
                API.get(EPTW_ROLE_URL),
                API.get(INCIDENT_ROLE_URL),
                API.get(INSPECTION_ROLE_URL),
                API.get(PLANT_ROLE_URL),
                API.get(GROUP_EHS_ROLE_URL),
                API.get(REPORT_ROLE_URL)
            ]);

            setAllRoles({
                country: [],
                ehs: ehsRes.data || [],
                eptw: eptwRes.data || [],
                incident: incidentRes.data || [],
                inspection: inspectionRes.data || [],
                plant: plantRes.data || [],
                groupEhs: groupEhsRes.data || [],
                report: reportRes.data || []
            });
        } catch (error) {
            cogoToast.error('Failed to fetch roles');
        }
    };

    // Get unique organizations from all users
    const getUniqueOrganizations = () => {
        const organizations = allUsers.map(user =>
            user.type === 'Internal' ? 'STT GDC' : (user.company || 'External')
        );
        return [...new Set(organizations)].sort();
    };

    const uniqueOrganizations = getUniqueOrganizations();

    // Filter organizations based on search query
    const filteredOrganizations = uniqueOrganizations.filter(org =>
        org.toLowerCase().includes(organizationSearchQuery.toLowerCase())
    );

    const filterUsers = (users) => {
        let filtered = users;

        // Apply search filter
        if (searchQuery.trim() !== '') {
            filtered = filtered.filter((user) => {
                return user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    user.email.toLowerCase().includes(searchQuery.toLowerCase());
            });
        }

        // Apply organization filter
        if (selectedOrganization !== '') {
            filtered = filtered.filter((user) => {
                const userOrganization = user.type === 'Internal' ? 'STT GDC' : (user.company || 'External');
                return userOrganization === selectedOrganization;
            });
        }

        return filtered;
    };

    // Pagination logic
    const filteredUsers = filterUsers(allUsers);
    const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentUsers = filteredUsers.slice(startIndex, endIndex);

    // Reset to first page when search query changes
    useEffect(() => {
        setCurrentPage(1);
    }, [searchQuery]);

    // Reset to first page when items per page changes
    useEffect(() => {
        setCurrentPage(1);
    }, [itemsPerPage]);

    // Reset to first page when organization filter changes
    useEffect(() => {
        setCurrentPage(1);
    }, [selectedOrganization]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const handleItemsPerPageChange = (newItemsPerPage) => {
        setItemsPerPage(newItemsPerPage);
    };

    const handleOrganizationSelect = (organization) => {
        setSelectedOrganization(organization);
        setOrganizationDropdownOpen(false);
        setOrganizationSearchQuery('');
    };

    const handleOrganizationClear = () => {
        setSelectedOrganization('');
        setOrganizationDropdownOpen(false);
        setOrganizationSearchQuery('');
    };

    const viewUserRoles = async (user) => {
        setLoading(true);
        setSelectedUser(user);
        
        try {
            // Get user's custom roles
            const userResponse = await API.get(USERS_URL_WITH_ID(user.id));
            if (userResponse.status === 200) {
                if (userResponse.data.customRoles) {
                    setUserRoles(userResponse.data.customRoles);
                } else {
                    setUserRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] });
                }
            }



            setShowModal(true);
        } catch (error) {
            cogoToast.error('Failed to fetch user roles');
        } finally {
            setLoading(false);
        }
    };

    const getRoleName = (roleId, category) => {
        const role = allRoles[category]?.find(r => r.id === roleId);
        return role ? role.name : 'Unknown Role';
    };

    const getModuleIcon = (category) => {
        const icons = {
            'ehs': 'mdi-eye',
            'eptw': 'mdi-file-document-edit',
            'incident': 'mdi-alert-circle',
            'inspection': 'mdi-clipboard-check',
            'plant': 'mdi-factory',
            'groupEhs': 'mdi-shield-account',
            'report': 'mdi-chart-line'
        };
        return icons[category] || 'mdi-dots-horizontal';
    };

    const renderRoleSection = (title, category, roles) => {
        if (!roles || roles.length === 0) return null;

        return (
            <div className="module-group">
                <div className="module-header">
                    <h6 className="module-title">
                        <i className={`mdi ${getModuleIcon(category)} me-2`}></i>
                        {title}
                    </h6>
                </div>
                <div className="role-badges-grid">
                    {roles.map((roleId, index) => (
                        <div key={index} className="role-badge-item">
                            <span className="role-badge">
                                <i className="mdi mdi-check-circle text-success me-2"></i>
                                {getRoleName(roleId, category)}
                            </span>
                        </div>
                    ))}
                </div>
            </div>
        );
    };



    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body">
                                <h4 className="card-title">View Specific User Role</h4>
                                <p className="card-description">Search and view detailed role assignments for specific users</p>
                                
                                <div className="row">
                                    <div className="col-12">
                                        <div className="row mb-4">
                                            <div className="col-md-6">
                                                <label className="form-label">Search Users</label>
                                                <input
                                                    type="text"
                                                    className="form-control"
                                                    placeholder="Search by name or email..."
                                                    value={searchQuery}
                                                    onChange={(e) => setSearchQuery(e.target.value)}
                                                />
                                            </div>
                                        </div>

                                        <CardOverlay>
                                            <div className="table-responsive">
                                                <table className="table table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>Name</th>
                                                            <th>Email</th>
                                                            <th>Organization</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {currentUsers.length > 0 ? (
                                                            currentUsers.map((user) => (
                                                                <tr key={user.id}>
                                                                    <td>{user.firstName}</td>
                                                                    <td>{user.email}</td>
                                                                    <td>{user.type === 'Internal' ? 'STT GDC' : (user.company || 'External')}</td>
                                                                    <td>
                                                                        <Button
                                                                            variant="outline-primary"
                                                                            size="sm"
                                                                            onClick={() => viewUserRoles(user)}
                                                                            disabled={loading}
                                                                        >
                                                                            <i className="mdi mdi-eye me-1"></i>
                                                                            View Roles
                                                                        </Button>
                                                                    </td>
                                                                </tr>
                                                            ))
                                                        ) : (
                                                            <tr>
                                                                <td colSpan="4" className="text-center py-4">
                                                                    {searchQuery || selectedOrganization
                                                                        ? 'No users found matching your filters.'
                                                                        : 'No users available.'}
                                                                </td>
                                                            </tr>
                                                        )}
                                                    </tbody>
                                                </table>
                                            </div>

                                            {/* Pagination */}
                                            {filteredUsers.length > 0 && (
                                                <div className="d-flex justify-content-end align-items-center mt-3">
                                                    <div className="d-flex align-items-center me-3">
                                                        <span className="me-2">Show:</span>
                                                        <select
                                                            className="form-select form-select-sm"
                                                            style={{ width: 'auto' }}
                                                            value={itemsPerPage}
                                                            onChange={(e) => handleItemsPerPageChange(parseInt(e.target.value))}
                                                        >
                                                            <option value={5}>5</option>
                                                            <option value={10}>10</option>
                                                            <option value={20}>20</option>
                                                        </select>
                                                        <span className="ms-2">entries</span>
                                                    </div>

                                                    <div className="d-flex align-items-center">
                                                        <span className="me-3">
                                                            Showing {startIndex + 1} to {Math.min(endIndex, filteredUsers.length)} of {filteredUsers.length} entries
                                                        </span>

                                                    <nav>
                                                        <ul className="pagination pagination-sm mb-0">
                                                            <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                                                                <button
                                                                    className="page-link"
                                                                    onClick={() => handlePageChange(currentPage - 1)}
                                                                    disabled={currentPage === 1}
                                                                >
                                                                    Previous
                                                                </button>
                                                            </li>

                                                            {Array.from({ length: totalPages }, (_, index) => {
                                                                const page = index + 1;
                                                                // Show first page, last page, current page, and pages around current page
                                                                if (
                                                                    page === 1 ||
                                                                    page === totalPages ||
                                                                    (page >= currentPage - 1 && page <= currentPage + 1)
                                                                ) {
                                                                    return (
                                                                        <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                                                                            <button
                                                                                className="page-link"
                                                                                onClick={() => handlePageChange(page)}
                                                                            >
                                                                                {page}
                                                                            </button>
                                                                        </li>
                                                                    );
                                                                } else if (
                                                                    page === currentPage - 2 ||
                                                                    page === currentPage + 2
                                                                ) {
                                                                    return (
                                                                        <li key={page} className="page-item disabled">
                                                                            <span className="page-link">...</span>
                                                                        </li>
                                                                    );
                                                                }
                                                                return null;
                                                            })}

                                                            <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                                                                <button
                                                                    className="page-link"
                                                                    onClick={() => handlePageChange(currentPage + 1)}
                                                                    disabled={currentPage === totalPages}
                                                                >
                                                                    Next
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </nav>
                                                </div>
                                                </div>
                                            )}
                                        </CardOverlay>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* User Roles Modal */}
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="user-roles-modal"
                className='extra-large view-roles-modal'
                backdrop="static"
                keyboard={false}
            >
                <Modal.Header className="view-roles-header">
                    <div className='w-100 d-flex align-items-center justify-content-between'>
                        <div className="header-info">
                            <h4 className='mb-1 modal-title'>Role Assignments</h4>
                            <div className="user-info">
                                <span className="user-name">{selectedUser?.firstName}</span>
                                <span className="user-email text-muted">({selectedUser?.email})</span>
                            </div>
                        </div>
                        <div className="header-actions">
                            <Button variant="outline-secondary" size="sm" onClick={() => setShowModal(false)}>
                                <i className="mdi mdi-close"></i>
                            </Button>
                        </div>
                    </div>
                </Modal.Header>
                <Modal.Body className="view-roles-modal-body p-0">
                    {selectedUser && (
                        <div className="modal-content-wrapper">
                            <div className="single-column-layout">
                                <div className="user-info-container">
                                    <div className="section-header">
                                        <h5 className="section-title">
                                            <i className="mdi mdi-account-circle me-2 text-primary"></i>
                                            User Information
                                        </h5>
                                        <p className="section-subtitle text-muted">Basic user details and organization</p>
                                    </div>

                                    <div className="user-details-card">
                                        <div className="detail-item">
                                            <span className="detail-label">Name:</span>
                                            <span className="detail-value">{selectedUser.firstName}</span>
                                        </div>
                                        <div className="detail-item">
                                            <span className="detail-label">Email:</span>
                                            <span className="detail-value">{selectedUser.email}</span>
                                        </div>
                                        <div className="detail-item">
                                            <span className="detail-label">Organization:</span>
                                            <span className="detail-value">{selectedUser.type === 'Internal' ? 'STT GDC' : (selectedUser.company || 'External')}</span>
                                        </div>
                                    </div>

                                    <div className="section-header mt-4">
                                        <h5 className="section-title">
                                            <i className="mdi mdi-shield-account me-2 text-success"></i>
                                            Application Roles
                                        </h5>
                                        <p className="section-subtitle text-muted">System-wide role assignments</p>
                                    </div>

                                    <div className="application-roles-container">
                                        {renderRoleSection('Group EHS', 'groupEhs', userRoles.groupEhs)}
                                        {renderRoleSection('EHS Observation', 'ehs', userRoles.ehs)}
                                        {renderRoleSection('ePermit to Work', 'eptw', userRoles.eptw)}
                                        {renderRoleSection('Incident Reporting', 'incident', userRoles.incident)}
                                        {renderRoleSection('Inspection', 'inspection', userRoles.inspection)}
                                        {renderRoleSection('Plant & Equipment', 'plant', userRoles.plant)}
                                        {renderRoleSection('Reports', 'report', userRoles.report)}

                                        {/* Check if user has no application roles */}
                                        {Object.values(userRoles).every(roles => !roles || roles.length === 0) && (
                                            <div className="empty-state">
                                                <i className="mdi mdi-account-off text-muted" style={{ fontSize: '3rem' }}></i>
                                                <p className="text-muted mt-3">No application roles assigned to this user.</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Fixed Footer */}
                            <div className="modal-fixed-footer">
                                <div className="footer-actions">
                                    <Button variant="outline-secondary" onClick={() => setShowModal(false)}>
                                        <i className="mdi mdi-close me-1"></i>Close
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}
                </Modal.Body>
            </Modal>
        </>
    );
};

export default ViewSpecificUserRole;
